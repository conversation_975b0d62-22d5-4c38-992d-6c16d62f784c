import { Node } from "../../../pocketflow";
import { SharedStore } from "../types";
import { emitGraphStatus, emitProgress, emitComplete } from "../utils/events";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";
import { BUCKET_NAME } from "@/constants";
import { callLlm_openrouter } from "@/Agents/shared/callLlm_openrouter";

// Detect environment
const isNode = typeof window === 'undefined';

export class CombineTutorial extends Node<SharedStore> {
  async prep(shared: SharedStore) {
    // Emit graph status to indicate this node is starting
    emitGraphStatus("CombineTutorial", 0, "Starting tutorial compilation");

    const projectName = shared.project_name!;
    const outputBaseDir = shared.output_dir || "output"; // Default output dir
    const outputPath = `${outputBaseDir}/${projectName}`;
    const repoUrl = shared.repo_url;
    
   

    emitGraphStatus("CombineTutorial", 10, "Gathering tutorial components");

    // Get potentially translated data
    const relationshipsData = shared.relationships!;
    const chapterOrder = shared.chapter_order || [];
    const abstractions = shared.abstractions || [];
    const chaptersContent = shared.chapters || [];

    // --- Generate Mermaid Diagram ---
    emitGraphStatus("CombineTutorial", 20, "Generating relationship diagram");

    const mermaidLines = ["flowchart TD"];
    // Add nodes for each abstraction using potentially translated names
    for (let i = 0; i < abstractions.length; i++) {
      const nodeId = `A${i}`;
      // Use potentially translated name, sanitize for Mermaid ID and label
      const sanitizedName = abstractions[i].name.replace(/"/g, "");
      const nodeLabel = sanitizedName; // Using sanitized name only
      mermaidLines.push(`    ${nodeId}["${nodeLabel}"]`); // Node label uses potentially translated name
    }

    // Add edges for relationships using potentially translated labels
    for (const rel of relationshipsData.details) {
      const fromNodeId = `A${rel.from}`;
      const toNodeId = `A${rel.to}`;
      // Use potentially translated label, sanitize
      let edgeLabel = rel.label.replace(/"/g, "").replace(/\n/g, " "); // Basic sanitization
      const maxLabelLen = 30;
      if (edgeLabel.length > maxLabelLen) {
        edgeLabel = edgeLabel.substring(0, maxLabelLen - 3) + "...";
      }
      mermaidLines.push(`    ${fromNodeId} -- "${edgeLabel}" --> ${toNodeId}`); // Edge label uses potentially translated label
    }

    const mermaidDiagram = mermaidLines.join("\n");
    // --- End Mermaid ---

    // --- Prepare index.md content ---
    emitGraphStatus("CombineTutorial", 30, "Creating index page");

    let indexContent = `# Tutorial: ${projectName}\n\n`;
    indexContent += `${relationshipsData.summary}\n\n`; // Use the potentially translated summary directly
    // Keep fixed strings in English
    if (repoUrl) {
      indexContent += `**Source Repository:** [${repoUrl}](${repoUrl})\n\n`;
    }

    // Add Mermaid diagram for relationships (diagram itself uses potentially translated names/labels)
    indexContent += "```mermaid\n";
    indexContent += mermaidDiagram + "\n";
    indexContent += "```\n\n";

    // Keep fixed strings in English
    indexContent += `## Chapters\n\n`;

    emitGraphStatus("CombineTutorial", 40, "Preparing chapter files");

    const chapterFiles = [];
    // Generate chapter links based on the determined order, using potentially translated names
    for (let i = 0; i < chapterOrder.length; i++) {
      const abstractionIndex = chapterOrder[i];
      // Ensure index is valid and we have content for it
      if (
        0 <= abstractionIndex &&
        abstractionIndex < abstractions.length &&
        i < chaptersContent.length
      ) {
        const abstractionName = abstractions[abstractionIndex].name; // Potentially translated name
        // Sanitize potentially translated name for filename
        const safeName = abstractionName
          .replace(/[^a-zA-Z0-9]/g, "_")
          .toLowerCase();
        const filename = `${(i + 1)
          .toString()
          .padStart(2, "0")}_${safeName}.md`;
        indexContent += `${i + 1}. [${abstractionName}](${filename})\n`; // Use potentially translated name in link text

        // Add attribution to chapter content (using English fixed string)
        let chapterContent = chaptersContent[i]; // Potentially translated content
        if (!chapterContent.endsWith("\n\n")) {
          chapterContent += "\n\n";
        }
        // Keep fixed strings in English
        chapterContent += `---\n\nGenerated by [Generative Pangea](https://www.generativepangea.com) - AI-powered documentation solutions`;

        // Store filename and corresponding content
        chapterFiles.push({ filename, content: chapterContent });
      } else {
        emitGraphStatus("CombineTutorial", 45, `Warning: Mismatch at index ${i} (abstraction index ${abstractionIndex})`);
        console.log(
          `Warning: Mismatch between chapter order, abstractions, or content at index ${i} (abstraction index ${abstractionIndex}). Skipping file generation for this entry.`
        );
      }
    }

    // Add attribution to index content (using English fixed string)
    indexContent += `---\n\nGenerated by [Generative Pangea](https://www.generativepangea.com) - AI-powered documentation solutions`;

    emitGraphStatus("CombineTutorial", 50, "Preparation complete, ready to write files");

    return {
      outputPath,
      indexContent,
      chapterFiles, // Array of {filename: string, content: string}
      projectName,
      project_description: relationshipsData.summary,
      repoUrl,
      language: shared.language || "english"
    };
  }

  async exec(prepRes: any): Promise<string> {
    const { outputPath, indexContent, chapterFiles, projectName, project_description, repoUrl, language } = prepRes;
  
    if (isNode) {
      // Node.js environment - use filesystem
      return this.saveToFilesystem(outputPath, indexContent, chapterFiles);
    } else {
      // Browser environment - use Supabase
      return this.saveToSupabase(indexContent, chapterFiles, projectName,project_description, repoUrl, language);
    }
  }

  private async saveToFilesystem(outputPath: string, indexContent: string, chapterFiles: any[]): Promise<string> {
    emitGraphStatus("CombineTutorial", 60, `Creating output directory: ${outputPath}`);
    console.log(`Combining tutorial into directory: ${outputPath}`);

    try {
      // Import fs dynamically to avoid browser issues
      const fs = await import('fs');

      // Create directory if it doesn't exist
      fs.mkdirSync(outputPath, { recursive: true });

      // Write index.md
      emitGraphStatus("CombineTutorial", 70, "Writing index.md file");
      const indexFilepath = `${outputPath}/index.md`;
      fs.writeFileSync(indexFilepath, indexContent, "utf-8");
      console.log(`  - Wrote ${indexFilepath}`);

      // Write chapter files
      emitGraphStatus("CombineTutorial", 80, `Writing ${chapterFiles.length} chapter files`);
      for (const chapterInfo of chapterFiles) {
        const chapterFilepath = `${outputPath}/${chapterInfo.filename}`;
        fs.writeFileSync(chapterFilepath, chapterInfo.content, "utf-8");
        console.log(`  - Wrote ${chapterFilepath}`);
      }

      emitGraphStatus("CombineTutorial", 90, "All files written successfully");
      return outputPath; // Return the final path
    } catch (error) {
      console.error("Error writing files:", error);
      throw error;
    }
  }

  private async saveToSupabase(indexContent: string, chapterFiles: any[], projectName: string, project_description: string, repoUrl: string, language: string): Promise<string> {
    emitGraphStatus("CombineTutorial", 60, `Preparing to save tutorial to Supabase`);
    console.log(`Saving tutorial to Supabase: ${projectName}`);

    if (!supabase) {
      throw new Error("Supabase client not initialized. Check your environment variables.");
    }

    try {
      // First, let's try to create the bucket if it doesn't exist
      // This is a separate try/catch to continue even if bucket creation fails (it might already exist)
      // try {
      //   const { data: bucketData, error: bucketError } = await supabase.storage.createBucket('tutorials', {
      //     public: true,
      //     fileSizeLimit: 50000000, // 50MB limit
      //   });

      //   if (bucketError && !bucketError.message.includes('already exists')) {
      //     console.warn("Warning creating bucket:", bucketError);
      //     // Continue anyway - bucket might already exist
      //   } else {
      //     console.log("Created or verified bucket: tutorials");
      //   }
      // } catch (bucketErr) {
      //   // Just log the error but continue - bucket might already exist
      //   console.warn("Warning during bucket operation:", bucketErr);
      // }

      // Create a unique ID for this tutorial
      const tutorialId = `${projectName.replace(/[^a-zA-Z0-9-_]/g, '-')}-${Date.now()}`;

      // Save the index file to Supabase Storage
      emitGraphStatus("CombineTutorial", 70, "Saving index.md file");
      const indexPath = `${tutorialId}/index.md`;
      const indexBlob = new Blob([indexContent], { type: 'text/markdown' });

      const { data: indexData, error: indexError } = await supabase.storage
        .from(BUCKET_NAME)
        .upload(indexPath, indexBlob, {
          contentType: 'text/markdown',
          upsert: true
        });

      if (indexError) {
        console.error("Error uploading index.md:", indexError);
        // Show toast notification about the error
        toast({
          title: "Error Saving Tutorial",
          description: `Failed to upload index file: ${indexError.message}`,
          variant: "destructive"
        });
        throw indexError;
      }

      console.log(`Saved index.md to Supabase: ${indexPath}`);

      // Get public URL for the index file
      const { data: indexUrlData } = supabase.storage
        .from('tutorials')
        .getPublicUrl(indexPath);

      const indexUrl = indexUrlData.publicUrl;

      // Save chapter files
      emitGraphStatus("CombineTutorial", 80, `Saving ${chapterFiles.length} chapter files`);
      const chapterUrls = [];

      // Loop through chapter files
      for (const chapterInfo of chapterFiles) {
        const chapterPath = `${tutorialId}/${chapterInfo.filename}`;
        const chapterBlob = new Blob([chapterInfo.content], { type: 'text/markdown' });

        const { data: chapterData, error: chapterError } = await supabase.storage
          .from('tutorials')
          .upload(chapterPath, chapterBlob, {
            contentType: 'text/markdown',
            upsert: true
          });

        if (chapterError) {
          console.error(`Error uploading chapter ${chapterInfo.filename}:`, chapterError);
          // Show toast notification for chapter upload error
          toast({
            title: "Error Saving Chapter",
            description: `Failed to upload ${chapterInfo.filename}: ${chapterError.message}`,
            variant: "destructive"
          });
          throw chapterError;
        }

        console.log(`Saved chapter to Supabase: ${chapterPath}`);

        // Get public URL for the chapter
        const { data: chapterUrlData } = supabase.storage
          .from('tutorials')
          .getPublicUrl(chapterPath);

        chapterUrls.push({
          filename: chapterInfo.filename,
          url: chapterUrlData.publicUrl
        });
      }
      // End loop
      // Save tutorial metadata to Supabase database
      try {
        console.log("Saving tutorial metadata to database with:", {
          tutorial_id: tutorialId,
          project_name: projectName,
          index_url: indexUrl,
          chapter_urls: chapterUrls
        });

        const prompt = `<role>
  <identity>You are a summarization assistant trained to extract engaging yet concise key takeaways.</identity>
  <primary_goal>Your goal is to analyze a project description written in Markdown and produce an informative and catchy summary of about 10 words in the target language ${language}</primary_goal>
</role>

<static_context>
  <background_information>All project descriptions are given in short Markdown-formatted paragraphs. These might include lists, headers, or inline formatting like bold or italic text.</background_information>
  <domain_details>Summaries should be ~10 words. They must be intelligible, concise, informative, and attract attention. Do not exceed 12 words or drop below 8.</domain_details>
</static_context>

<rules>
  <dos_and_donts>
    <do>Always extract the core concept or purpose of the project.</do>
    <do>Include unique or standout elements that make the project notable.</do>
    <do>Use natural, fluid phrasing to ensure readability and interest.</do>
    <dont>Do not include markdown syntax in the summary output.</dont>
    <dont>Do not copy and paste phrases verbatim unless they're uniquely catchy.</dont>
  </dos_and_donts>
</rules>

<chain_of_thought>
  <process_list>
    <step>Input Analysis</step>
    <step>Key Idea Extraction</step>
    <step>Catchy Framing</step>
    <step>Word Count Check</step>
    <step>Final Refinement</step>
  </process_list>
  <process_usage_instructions>
    <step>
      <name>Input Analysis</name>
      <description>Read the paragraph to understand the project's theme, scope, and impact.</description>
    </step>
    <step>
      <name>Key Idea Extraction</name>
      <description>Identify the project's main goal, unique elements, and target audience.</description>
    </step>
    <step>
      <name>Catchy Framing</name>
      <description>Rephrase the key ideas into a catchy, punchy summary of ~10 words.</description>
    </step>
    <step>
      <name>Word Count Check</name>
      <description>Ensure the final output is between 8 and 12 words.</description>
    </step>
    <step>
      <name>Final Refinement</name>
      <description>Polish phrasing for readability, flow, and engagement.</description>
    </step>
  </process_usage_instructions>
</chain_of_thought>

<desired_output_format>
  <format>Plain text string, without bullet points or Markdown symbols. Should be ~10 words in total.</format>
</desired_output_format>

<style_guidelines>
  <tone>Professional but energetic</tone>
  <language>Natural, easy to understand, slightly catchy</language>
</style_guidelines>

<input>
  <project_description>${project_description}</project_description>
</input>
`;


        const llm_project_summary= await callLlm_openrouter ( {prompt, use_cache: true, temperature: 0.7, model: "google/gemma-3n-e4b-it:free"}); // free model

        emitGraphStatus("CombineTutorial", 85, "Generating project summary");
        
        const { data: metaData, error: metaError } = await supabase
          .from('tutorial_metadata')
          .insert({
            tutorial_id: tutorialId,
            project_name: projectName,
            index_url: indexUrl,
            chapter_urls: chapterUrls,
            description: llm_project_summary,
            repo_url: repoUrl,
            language: language,
           
          })
          .select();

        if (metaError) {
          console.error("Error saving to tutorial_metadata:", metaError);
          // Show toast notification
          toast({
            title: "Error Saving Metadata",
            description: `Failed to save tutorial metadata: ${metaError.message}`,
            variant: "destructive"
          });
          throw metaError;
        }

        // Success toast notification
        toast({
          title: "Tutorial Saved",
          description: "Tutorial files and metadata saved successfully",
        });
      } catch (metaErr) {
        console.error("Error in metadata saving:", metaErr);
        // If metadata fails but files saved, we should still return the ID
        toast({
          title: "Partial Success",
          description: "Tutorial files saved but metadata failed",
          variant: "destructive"
        });
      }

      emitGraphStatus("CombineTutorial", 90, "All files saved to Supabase successfully");
      return tutorialId; // Return the tutorial ID for reference
    } catch (error) {
      console.error("Error saving to Supabase:", error);
      // Show final error toast
      toast({
        title: "Error Saving Tutorial",
        description: `An unexpected error occurred: ${error.message || error}`,
        variant: "destructive"
      });
      throw error;
    }
  }

  async post(
    shared: SharedStore,
    _: any,
    execRes: string
  ): Promise<string | undefined> {
    if (isNode) {
      // Node.js environment
      emitGraphStatus("CombineTutorial", 95, `Storing output path in shared store: ${execRes}`);
      shared.final_output_dir = execRes; // Store the output path

      // Emit progress event
      emitProgress("Tutorial Compilation", 100, `Tutorial generated successfully in: ${execRes}`);

      // Final graph status
      emitGraphStatus("CombineTutorial", 100, "Tutorial compilation complete");

      // Emit complete event to signal the end of the flow
      emitComplete({ success: true, message: "Tutorial successfully generated" });
    } else {
      // Browser environment
      emitGraphStatus("CombineTutorial", 95, `Tutorial saved to Supabase with ID: ${execRes}`);
      shared.final_output_dir = execRes; // Store the tutorial ID

      if (supabase) {
        // Get the public URL for the index file
        const indexPath = `${execRes}/index.md`;
        const { data: urlData } = supabase.storage
          .from('tutorials')
          .getPublicUrl(indexPath);

        // Emit progress event
        emitProgress("Tutorial Compilation", 100, `Tutorial generated successfully. Access at: ${urlData.publicUrl}`);

        // Final graph status
        emitGraphStatus("CombineTutorial", 100, "Tutorial compilation complete");

        // Emit complete event with tutorial ID for browser environment
        emitComplete({ 
          success: true, 
          message: "Tutorial successfully generated",
          tutorialId: execRes // Include the tutorial ID in the complete event
        });
      } else {
        emitGraphStatus("CombineTutorial", 100, "Tutorial compilation complete");
        emitProgress("Tutorial Compilation", 100, "Tutorial generated successfully");
      }
    }

    return "default";
  }
}
