
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import NavBar from "@/components/NavBar";
import { getUsageStats, getRecentUsage } from "@/utils/usageAnalytics";
import UsageOverviewCards from "@/components/admin/UsageOverviewCards";
import UsageChartsPanel from "@/components/admin/UsageChartsPanel";
import RecentUsageTable from "@/components/admin/RecentUsageTable";
import { AlertTriangle } from "lucide-react";

const AdminDashboard = () => {
  // TODO: Add admin role check here when authentication is implemented
  // const { user, isAdmin } = useAuth();
  // if (!isAdmin) return <Navigate to="/dashboard" />;

  const { data: usageStats, isLoading: statsLoading, error: statsError } = useQuery({
    queryKey: ['admin-usage-stats'],
    queryFn: () => getUsageStats(), // Get all users' stats
  });

  const { data: recentUsage, isLoading: recentLoading, error: recentError } = useQuery({
    queryKey: ['admin-recent-usage'],
    queryFn: () => getRecentUsage(100), // Get last 100 records
  });

  if (statsError || recentError) {
    return (
      <div className="min-h-screen bg-gray-50">
        <NavBar />
        <div className="container mx-auto px-4 py-8">
          <Card className="border-red-200 bg-red-50">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5 text-red-600" />
                <CardTitle className="text-red-800">Error Loading Dashboard</CardTitle>
              </div>
              <CardDescription className="text-red-600">
                Failed to load usage analytics. Please try again later.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <NavBar />
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="text-gray-600 mt-2">Monitor API usage, costs, and system performance</p>
        </div>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="recent">Recent Activity</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <UsageOverviewCards 
              stats={usageStats} 
              isLoading={statsLoading} 
            />
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <UsageChartsPanel 
              stats={usageStats} 
              recentUsage={recentUsage}
              isLoading={statsLoading || recentLoading} 
            />
          </TabsContent>

          <TabsContent value="recent" className="space-y-6">
            <RecentUsageTable 
              usage={recentUsage} 
              isLoading={recentLoading} 
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AdminDashboard;
