
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";

interface RecentUsageTableProps {
  usage?: any[];
  isLoading: boolean;
}

const RecentUsageTable = ({ usage, isLoading }: RecentUsageTableProps) => {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-4 w-48" />
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex space-x-4">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-20" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!usage || usage.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent API Usage</CardTitle>
          <CardDescription>Latest API calls and their details</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-center text-muted-foreground py-8">No usage data available</p>
        </CardContent>
      </Card>
    );
  }

  const formatCurrency = (amount: number) => 
    new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);

  const getModelBadgeColor = (model: string) => {
    if (model.includes('gpt-4')) return 'destructive';
    if (model.includes('claude')) return 'secondary';
    if (model.includes('gemini')) return 'default';
    return 'outline';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent API Usage</CardTitle>
        <CardDescription>
          Latest {usage.length} API calls with costs and token usage
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Timestamp</TableHead>
                <TableHead>Model</TableHead>
                <TableHead>Tokens</TableHead>
                <TableHead>Cost</TableHead>
                <TableHead>User ID</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {usage.map((record) => (
                <TableRow key={record.id}>
                  <TableCell className="font-mono text-sm">
                    {format(new Date(record.created_at), 'MMM dd, HH:mm:ss')}
                  </TableCell>
                  <TableCell>
                    <Badge variant={getModelBadgeColor(record.model)}>
                      {record.model.split('/').pop() || record.model}
                    </Badge>
                  </TableCell>
                  <TableCell className="font-mono">
                    {record.total_tokens ? record.total_tokens.toLocaleString() : 'N/A'}
                  </TableCell>
                  <TableCell className="font-mono">
                    {record.cost ? formatCurrency(record.cost) : 'N/A'}
                  </TableCell>
                  <TableCell className="font-mono text-xs">
                    {record.user_id ? 
                      `${record.user_id.substring(0, 8)}...` : 
                      'Anonymous'
                    }
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};

export default RecentUsageTable;
