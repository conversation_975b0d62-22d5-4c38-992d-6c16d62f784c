
import React from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

const Landing = () => {
  return (
    <div className="min-h-screen flex flex-col">
      {/* SEO Meta Tags would go in index.html */}
      
      {/* Hero Section */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="container mx-auto px-4 py-3 flex items-center justify-between">
          <div className="flex items-center">
            <Link to="/" className="flex items-center cursor-pointer">
              <i className="fa-solid fa-book-open text-primary text-2xl mr-2"></i>
              <span className="text-xl font-bold text-gray-800">CodeTutor</span>
            </Link>
          </div>
          <div className="flex items-center space-x-4">
            <a href="#pricing" className="text-gray-600 hover:text-primary font-medium">
              Pricing
            </a>
            <Link to="/gallery" className="text-gray-600 hover:text-primary font-medium">
              Gallery
            </Link>
            <Link to="/dashboard">
              <Button variant="outline">Sign In</Button>
            </Link>
            <Link to="/dashboard">
              <Button>Get Started</Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1">
        {/* Hero Section */}
        <section className="bg-gradient-to-r from-tutorial-primary to-tutorial-secondary py-24 text-white relative overflow-hidden">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="container mx-auto px-4 text-center relative z-10">
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
              Transform Code into
              <span className="block bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
                Knowledge
              </span>
            </h1>
            <p className="text-xl md:text-2xl mb-10 max-w-4xl mx-auto opacity-95 leading-relaxed">
              Generate comprehensive, beginner-friendly tutorials from any GitHub repository with the power of AI. 
              Turn complex codebases into step-by-step learning experiences.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4 mb-12">
              <Link to="/dashboard">
                <Button size="lg" className="bg-white text-tutorial-primary hover:bg-gray-100 text-lg px-8 py-4">
                  Start Free Trial
                  <i className="fa-solid fa-arrow-right ml-2"></i>
                </Button>
              </Link>
              <Link to="/gallery">
                <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10 text-lg px-8 py-4">
                  Explore Examples
                  <i className="fa-solid fa-play ml-2"></i>
                </Button>
              </Link>
            </div>
            <div className="flex justify-center items-center space-x-8 text-sm opacity-80">
              <div className="flex items-center">
                <i className="fa-solid fa-check-circle text-green-300 mr-2"></i>
                7-day free trial
              </div>
              <div className="flex items-center">
                <i className="fa-solid fa-check-circle text-green-300 mr-2"></i>
                No credit card required
              </div>
              <div className="flex items-center">
                <i className="fa-solid fa-check-circle text-green-300 mr-2"></i>
                Cancel anytime
              </div>
            </div>
          </div>
        </section>

        {/* Social Proof */}
        <section className="py-12 bg-gray-50 border-b">
          <div className="container mx-auto px-4 text-center">
            <p className="text-gray-600 mb-6">Trusted by developers from</p>
            <div className="flex justify-center items-center space-x-12 opacity-60">
              <div className="text-2xl font-bold text-gray-800">GitHub</div>
              <div className="text-2xl font-bold text-gray-800">Microsoft</div>
              <div className="text-2xl font-bold text-gray-800">Google</div>
              <div className="text-2xl font-bold text-gray-800">Meta</div>
              <div className="text-2xl font-bold text-gray-800">Netflix</div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-24 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold mb-6 text-gray-800">How It Works</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Our AI-powered platform analyzes your code and creates comprehensive tutorials in minutes, not hours.
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Feature 1 */}
              <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <CardHeader className="text-center pb-4">
                  <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i className="fa-solid fa-code text-tutorial-primary text-3xl"></i>
                  </div>
                  <CardTitle className="text-2xl mb-3">Repository Analysis</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base leading-relaxed">
                    We analyze your GitHub repository to understand its structure, dependencies, and core concepts. 
                    Our AI identifies the most important patterns and learning objectives.
                  </CardDescription>
                </CardContent>
              </Card>

              {/* Feature 2 */}
              <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <CardHeader className="text-center pb-4">
                  <div className="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i className="fa-solid fa-brain text-tutorial-primary text-3xl"></i>
                  </div>
                  <CardTitle className="text-2xl mb-3">AI-Powered Generation</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base leading-relaxed">
                    Our AI transforms complex code into clear, step-by-step tutorials with explanations, examples, 
                    and interactive code snippets that make learning intuitive.
                  </CardDescription>
                </CardContent>
              </Card>

              {/* Feature 3 */}
              <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <CardHeader className="text-center pb-4">
                  <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i className="fa-solid fa-book text-tutorial-primary text-3xl"></i>
                  </div>
                  <CardTitle className="text-2xl mb-3">Interactive Learning</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base leading-relaxed">
                    Navigate through chapters, explore code snippets, and understand concepts at your own pace. 
                    Perfect for onboarding new team members or documenting your projects.
                  </CardDescription>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section className="py-24 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold mb-6 text-gray-800">What Developers Say</h2>
              <p className="text-xl text-gray-600">Join thousands of developers who love CodeTutor</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Testimonial 1 */}
              <Card className="border-0 shadow-lg">
                <CardContent className="p-8">
                  <div className="flex mb-4">
                    {[...Array(5)].map((_, i) => (
                      <i key={i} className="fa-solid fa-star text-yellow-400"></i>
                    ))}
                  </div>
                  <p className="text-gray-700 mb-6 leading-relaxed">
                    "CodeTutor saved me weeks of documentation work. It generated comprehensive tutorials 
                    for our entire codebase in just a few hours. Game changer!"
                  </p>
                  <div className="flex items-center">
                    <img 
                      src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=64&h=64&fit=crop&crop=face" 
                      alt="Alex Chen" 
                      className="w-12 h-12 rounded-full mr-4"
                    />
                    <div>
                      <div className="font-semibold text-gray-800">Alex Chen</div>
                      <div className="text-sm text-gray-600">Senior Developer at TechCorp</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Testimonial 2 */}
              <Card className="border-0 shadow-lg">
                <CardContent className="p-8">
                  <div className="flex mb-4">
                    {[...Array(5)].map((_, i) => (
                      <i key={i} className="fa-solid fa-star text-yellow-400"></i>
                    ))}
                  </div>
                  <p className="text-gray-700 mb-6 leading-relaxed">
                    "Perfect for onboarding new team members. Instead of spending days explaining our 
                    architecture, I just share the CodeTutor tutorial. Everyone gets up to speed quickly."
                  </p>
                  <div className="flex items-center">
                    <img 
                      src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=64&h=64&fit=crop&crop=face" 
                      alt="Sarah Johnson" 
                      className="w-12 h-12 rounded-full mr-4"
                    />
                    <div>
                      <div className="font-semibold text-gray-800">Sarah Johnson</div>
                      <div className="text-sm text-gray-600">Team Lead at StartupXYZ</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Testimonial 3 */}
              <Card className="border-0 shadow-lg">
                <CardContent className="p-8">
                  <div className="flex mb-4">
                    {[...Array(5)].map((_, i) => (
                      <i key={i} className="fa-solid fa-star text-yellow-400"></i>
                    ))}
                  </div>
                  <p className="text-gray-700 mb-6 leading-relaxed">
                    "As an open-source maintainer, CodeTutor helps me create documentation that actually 
                    helps developers understand and contribute to my projects. Amazing tool!"
                  </p>
                  <div className="flex items-center">
                    <img 
                      src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=64&h=64&fit=crop&crop=face" 
                      alt="Michael Rodriguez" 
                      className="w-12 h-12 rounded-full mr-4"
                    />
                    <div>
                      <div className="font-semibold text-gray-800">Michael Rodriguez</div>
                      <div className="text-sm text-gray-600">Open Source Maintainer</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section id="pricing" className="py-24 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold mb-6 text-gray-800">Simple, Transparent Pricing</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Choose the plan that fits your needs. All plans include a 7-day free trial.
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {/* Starter Plan */}
              <Card className="border-2 border-gray-200 hover:border-tutorial-primary transition-colors duration-300">
                <CardHeader className="text-center pb-4">
                  <CardTitle className="text-2xl mb-2">Starter</CardTitle>
                  <CardDescription className="text-base">Perfect for individual developers</CardDescription>
                  <div className="mt-4">
                    <span className="text-4xl font-bold text-gray-800">Free</span>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ul className="space-y-3">
                    <li className="flex items-center">
                      <i className="fa-solid fa-check text-green-500 mr-3"></i>
                      <span>3 tutorials per month</span>
                    </li>
                    <li className="flex items-center">
                      <i className="fa-solid fa-check text-green-500 mr-3"></i>
                      <span>Public repositories only</span>
                    </li>
                    <li className="flex items-center">
                      <i className="fa-solid fa-check text-green-500 mr-3"></i>
                      <span>Basic templates</span>
                    </li>
                    <li className="flex items-center">
                      <i className="fa-solid fa-check text-green-500 mr-3"></i>
                      <span>Community support</span>
                    </li>
                  </ul>
                  <Link to="/dashboard" className="block">
                    <Button className="w-full mt-6" variant="outline">
                      Get Started Free
                    </Button>
                  </Link>
                </CardContent>
              </Card>

              {/* Professional Plan */}
              <Card className="border-2 border-tutorial-primary shadow-xl relative">
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-tutorial-primary text-white px-4 py-1 rounded-full text-sm font-medium">
                    Most Popular
                  </span>
                </div>
                <CardHeader className="text-center pb-4">
                  <CardTitle className="text-2xl mb-2">Professional</CardTitle>
                  <CardDescription className="text-base">For serious developers and small teams</CardDescription>
                  <div className="mt-4">
                    <span className="text-4xl font-bold text-gray-800">$29</span>
                    <span className="text-gray-600">/month</span>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ul className="space-y-3">
                    <li className="flex items-center">
                      <i className="fa-solid fa-check text-green-500 mr-3"></i>
                      <span>20 tutorials per month</span>
                    </li>
                    <li className="flex items-center">
                      <i className="fa-solid fa-check text-green-500 mr-3"></i>
                      <span>Private repositories</span>
                    </li>
                    <li className="flex items-center">
                      <i className="fa-solid fa-check text-green-500 mr-3"></i>
                      <span>Advanced file filtering</span>
                    </li>
                    <li className="flex items-center">
                      <i className="fa-solid fa-check text-green-500 mr-3"></i>
                      <span>Multiple output formats</span>
                    </li>
                    <li className="flex items-center">
                      <i className="fa-solid fa-check text-green-500 mr-3"></i>
                      <span>Email support</span>
                    </li>
                    <li className="flex items-center">
                      <i className="fa-solid fa-check text-green-500 mr-3"></i>
                      <span>Custom templates</span>
                    </li>
                  </ul>
                  <Link to="/dashboard" className="block">
                    <Button className="w-full mt-6 bg-tutorial-primary hover:bg-tutorial-primary/90">
                      Start Free Trial
                    </Button>
                  </Link>
                </CardContent>
              </Card>

              {/* Enterprise Plan */}
              <Card className="border-2 border-gray-200 hover:border-tutorial-primary transition-colors duration-300">
                <CardHeader className="text-center pb-4">
                  <CardTitle className="text-2xl mb-2">Enterprise</CardTitle>
                  <CardDescription className="text-base">For large teams and organizations</CardDescription>
                  <div className="mt-4">
                    <span className="text-4xl font-bold text-gray-800">$99</span>
                    <span className="text-gray-600">/month</span>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ul className="space-y-3">
                    <li className="flex items-center">
                      <i className="fa-solid fa-check text-green-500 mr-3"></i>
                      <span>Unlimited tutorials</span>
                    </li>
                    <li className="flex items-center">
                      <i className="fa-solid fa-check text-green-500 mr-3"></i>
                      <span>Team collaboration</span>
                    </li>
                    <li className="flex items-center">
                      <i className="fa-solid fa-check text-green-500 mr-3"></i>
                      <span>Custom branding</span>
                    </li>
                    <li className="flex items-center">
                      <i className="fa-solid fa-check text-green-500 mr-3"></i>
                      <span>Priority support</span>
                    </li>
                    <li className="flex items-center">
                      <i className="fa-solid fa-check text-green-500 mr-3"></i>
                      <span>API access</span>
                    </li>
                    <li className="flex items-center">
                      <i className="fa-solid fa-check text-green-500 mr-3"></i>
                      <span>SSO integration</span>
                    </li>
                  </ul>
                  <Link to="/dashboard" className="block">
                    <Button className="w-full mt-6" variant="outline">
                      Contact Sales
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-16 bg-tutorial-primary text-white">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              <div>
                <div className="text-4xl font-bold mb-2">50K+</div>
                <div className="text-blue-200">Tutorials Generated</div>
              </div>
              <div>
                <div className="text-4xl font-bold mb-2">15K+</div>
                <div className="text-blue-200">Happy Developers</div>
              </div>
              <div>
                <div className="text-4xl font-bold mb-2">500+</div>
                <div className="text-blue-200">Companies</div>
              </div>
              <div>
                <div className="text-4xl font-bold mb-2">98%</div>
                <div className="text-blue-200">Satisfaction Rate</div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-24 bg-gray-50">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-4xl font-bold mb-6 text-gray-800">Ready to Transform Your Code?</h2>
            <p className="text-xl mb-10 max-w-3xl mx-auto text-gray-600">
              Join thousands of developers who are using CodeTutor to create better documentation, 
              onboard team members faster, and share knowledge more effectively.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link to="/dashboard">
                <Button size="lg" className="bg-tutorial-primary text-white hover:bg-tutorial-primary/90 text-lg px-8 py-4">
                  Start Your Free Trial
                  <i className="fa-solid fa-rocket ml-2"></i>
                </Button>
              </Link>
              <Link to="/gallery">
                <Button size="lg" variant="outline" className="text-lg px-8 py-4">
                  View Examples
                </Button>
              </Link>
            </div>
            <p className="text-sm text-gray-500 mt-6">
              No credit card required • 7-day free trial • Cancel anytime
            </p>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-8">
            <div className="md:col-span-2">
              <div className="flex items-center mb-4">
                <i className="fa-solid fa-book-open text-tutorial-primary text-2xl mr-2"></i>
                <span className="text-xl font-bold">CodeTutor</span>
              </div>
              <p className="text-gray-400 mb-6 max-w-md">
                Transform any GitHub repository into a comprehensive, beginner-friendly tutorial with the power of AI. 
                Make your code accessible to everyone.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  <i className="fa-brands fa-twitter text-xl"></i>
                </a>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  <i className="fa-brands fa-github text-xl"></i>
                </a>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  <i className="fa-brands fa-linkedin text-xl"></i>
                </a>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  <i className="fa-brands fa-discord text-xl"></i>
                </a>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Product</h3>
              <ul className="space-y-2">
                <li><Link to="/gallery" className="text-gray-400 hover:text-white transition-colors">Gallery</Link></li>
                <li><a href="#pricing" className="text-gray-400 hover:text-white transition-colors">Pricing</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">API</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Integrations</a></li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Resources</h3>
              <ul className="space-y-2">
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Documentation</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Blog</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Help Center</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Community</a></li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Company</h3>
              <ul className="space-y-2">
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">About Us</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Careers</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Privacy Policy</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Terms of Service</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-12 pt-8 text-center text-gray-400">
            <p>&copy; {new Date().getFullYear()} CodeTutor. All rights reserved. Made with ❤️ for developers worldwide.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Landing;
