import { FetchRepo } from '../src/Agents/Code2Documentation/nodes/FetchRepo';
import { SharedStore } from '../src/Agents/Code2Documentation/types';

async function testFetchRepo() {
  // Create a test shared store with sample data
  const shared: SharedStore = {
    repo_url: 'https://github.com/nikitavoloboev/ts',
  //  local_dir: './local-repo',
    //github_token: '',
    selected_files: ['src/index.ts', 'package.json', 'README.md'],
    language: 'typescript',
    use_cache: true,
    max_abstraction_num: 10,
    final_output_dir: './output',
    project_name: 'tssdsd:asa sa skas aska'
  };

  // Create the node
  const fetchRepo = new FetchRepo();
  
  // Run the node
  console.log('Running FetchRepo node...');
  
  // Test prep method
  const prepResult = await fetchRepo.prep(shared);

  console.log('Prep result:', prepResult);
  
  // Test exec method
  const execResult = await fetchRepo.exec(prepResult);
  console.log('Exec result:', execResult);
  
  // Test post method
  const postResult = await fetchRepo.post(shared, prepResult, execResult);
  console.log('Post result:', postResult);
  
  // Check if files are stored in shared store

}

// Run the test
testFetchRepo().catch(console.error);