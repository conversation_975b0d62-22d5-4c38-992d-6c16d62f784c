// src/types.ts

/**
 * Shared store used across the PocketFlow.js tutorial generator
 */
export interface Abstraction {
  name: string;
  description: string;
  files: number[];
}

export interface RelationshipDetail {
  from: number;
  to: number;
  label: string;
}

export interface RelationshipResult {
  summary: string;
  details: RelationshipDetail[];
}

//   Initialize the shared dictionary with inputs from arguments and environment
export interface SharedStore {
  // Input parameters
  repo_url?: string;
  local_dir?: string; // Local directory to crawl
  project_name?: string;
  github_token?: string;
  output_dir?: string;


  // Settings based on arguments or defaults
  selected_files: string[];
  language: string;
  use_cache: boolean;
  max_abstraction_num: number;

   // Initialize empty lists/dictionaries for outputs that other parts will fill
  files?: [string, string][];             // Array of [path, content]    # Will be filled by FetchRepo
  abstractions?: Abstraction[];           // Will be filled by IdentifyAbstractions
  relationships?: RelationshipResult;     // Will be filled by AnalyzeRelationships
  chapter_order?: number[];                // Will be filled by OrderChapters
  chapters?: string[];                //Will be filled by WriteChapters
  final_output_dir:string           //Will be filled by CombineTutorial



  // Internal cache
 // llmCache?: Record<string, string>;

}
